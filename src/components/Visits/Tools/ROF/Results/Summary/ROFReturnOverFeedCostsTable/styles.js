import { StyleSheet } from 'react-native';
import fonts, {
  normalize,
} from '../../../../../../../constants/theme/variables/customFont';
import colors from '../../../../../../../constants/theme/variables/customColor';

export default StyleSheet.create({
  container: {
    marginHorizontal: normalize(16.5),
    marginTop: normalize(8),
    marginBottom: normalize(20),
    backgroundColor: 'red',
  },
  headerRow: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderBottomColor: colors.accordionBorder,
    paddingVertical: normalize(8),
    paddingHorizontal: normalize(4),
  },
  dataRow: {
    flexDirection: 'row',
    backgroundColor: colors.white,
    borderBottomColor: colors.accordionBorder,
    paddingVertical: normalize(12),
    paddingHorizontal: normalize(8),
  },
  labelColumn: {
    flex: 2,
    justifyContent: 'center',
    paddingRight: normalize(8),
  },
  valueColumn: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  headerText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '700',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    color: colors.grey1,
    textAlign: 'center',
  },
  labelText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    color: colors.grey1,
  },
  valueText: {
    fontFamily: fonts.HelveticaNeueRegular,
    fontWeight: '500',
    fontSize: normalize(12),
    lineHeight: normalize(16),
    letterSpacing: 0.15,
    color: colors.grey1,
    textAlign: 'center',
  },
});
