// modules
import React from 'react';
import { View, Text } from 'react-native';
import { useSelector } from 'react-redux';

// styles
import styles from './styles';

// helpers
import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../../helpers/appSettingsHelper';

// constants
import { ROF_DECIMAL_PLACES } from '../../../../../../../constants/toolsConstants/ROFConstants';
import { ROF_FIELDS } from '../../../../../../../constants/FormConstants';
import i18n from '../../../../../../../localization/i18n';

const ROFReturnOverFeedCostsTable = ({ summaryData }) => {
  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  // Extract current and previous data from summaryData
  const currentData =
    summaryData?.[ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS] || {};
  const previousData =
    summaryData?.[ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS] || {};

  const tableRows = [
    {
      key: ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY,
      label: i18n
        .t('returnOverFeedCostPerCowPerDay')
        .replaceAll('$', currencySymbol)
        .replaceAll('kg', weightUnit),
      currentValue:
        currentData[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY] || 0,
      previousValue:
        previousData[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_COW_PER_DAY] || 0,
    },
    {
      key: ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF,
      label: i18n
        .t('returnOverFeedCostPerKgOfBF')
        .replaceAll('$', currencySymbol)
        .replaceAll('kg', weightUnit),
      currentValue:
        currentData[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF] || 0,
      previousValue:
        previousData[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_KG_OF_BF] || 0,
    },
    {
      key: ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE,
      label: i18n
        .t('returnOverFeedCostPerLitre')
        .replaceAll('$', currencySymbol)
        .replaceAll('kg', weightUnit),
      currentValue:
        currentData[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE] || 0,
      previousValue:
        previousData[ROF_FIELDS.RETURN_OVER_FEED_COST_PER_LITRE] || 0,
    },
  ];

  return (
    <View style={styles.container}>
      {/* Table Header */}
      <View style={styles.headerRow}>
        <View style={styles.labelColumn}>
          <Text style={styles.headerText}></Text>
        </View>
        <View style={styles.valueColumn}>
          <Text style={styles.headerText}>{i18n.t('current')}</Text>
        </View>
        <View style={styles.valueColumn}>
          <Text style={styles.headerText}>{i18n.t('previous')}</Text>
        </View>
      </View>

      {/* Table Rows */}
      {tableRows.map(row => (
        <View key={row.key} style={styles.dataRow}>
          <View style={styles.labelColumn}>
            <Text style={styles.labelText}>{row.label}</Text>
          </View>
          <View style={styles.valueColumn}>
            <Text style={styles.valueText}>
              {convertInputNumbersToRegionalBasis(
                row.currentValue,
                ROF_DECIMAL_PLACES,
                true,
              )}
            </Text>
          </View>
          <View style={styles.valueColumn}>
            <Text style={styles.valueText}>
              {convertInputNumbersToRegionalBasis(
                row.previousValue,
                ROF_DECIMAL_PLACES,
                true,
              )}
            </Text>
          </View>
        </View>
      ))}
    </View>
  );
};

export default ROFReturnOverFeedCostsTable;
