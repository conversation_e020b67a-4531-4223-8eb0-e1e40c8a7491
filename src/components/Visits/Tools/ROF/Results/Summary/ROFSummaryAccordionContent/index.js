// modules
import React from 'react';
import { View, Text } from 'react-native';
import { useSelector } from 'react-redux';

// components
import ROFReturnOverFeedCostsTable from '../ROFReturnOverFeedCostsTable';

// styles
import styles from './styles';

// helpers
import { convertInputNumbersToRegionalBasis } from '../../../../../../../helpers/genericHelper';
import { truncateString } from '../../../../../../../helpers/alphaNumericHelper';
import {
  getCurrencyForTools,
  getWeightUnitByMeasure,
} from '../../../../../../../helpers/appSettingsHelper';

// constants
import { ROF_DECIMAL_PLACES } from '../../../../../../../constants/toolsConstants/ROFConstants';
import i18n from '../../../../../../../localization/i18n';

const ROFSummaryAccordionContent = props => {
  let { data } = props;

  const unitOfMeasure = useSelector(state => state.visit.visit?.unitOfMeasure);
  const currencies = useSelector(state => state.enums.enum?.currencies);
  const selectedCurrency = useSelector(
    state => state.visit.visit?.selectedCurrency,
  );

  const weightUnit = getWeightUnitByMeasure(unitOfMeasure);
  const currencySymbol = getCurrencyForTools(currencies, selectedCurrency);

  // Check if this is the special return over feed costs case
  if (data?._isReturnOverFeedCosts) {
    return <ROFReturnOverFeedCostsTable data={data} />;
  }

  // Regular accordion content for other sections
  return (
    <View style={styles.container}>
      {data &&
        Object.entries(data)?.map(([key, value]) => {
          // Skip the special flags
          if (key.startsWith('_')) {
            return null;
          }
          let isIngredient = typeof value == 'object';
          if (isIngredient) {
            return (
              <View
                style={styles.ingredientTextContainer}
                key={'_ROFSummaryAccordionContent-' + key}>
                <Text style={styles.ingredientHeadingText}>
                  {truncateString(i18n.t(key), 40)
                    .replaceAll('$', currencySymbol)
                    .replaceAll('kg', weightUnit)}
                </Text>
                {Object.keys(value).map(k => (
                  <View
                    style={styles.ingredientContainer}
                    key={'_ROFSummaryIngredientAccordionContent-' + k}>
                    <Text style={styles.accordionText}>
                      {truncateString(i18n.t(k), 40)
                        .replaceAll('$', currencySymbol)
                        .replaceAll('kg', weightUnit)}
                    </Text>
                    <Text style={styles.accordionText}>
                      {convertInputNumbersToRegionalBasis(
                        value[k],
                        ROF_DECIMAL_PLACES,
                        true,
                      )}
                    </Text>
                  </View>
                ))}
              </View>
            );
          } else {
            return (
              <View
                style={styles.textContainer}
                key={'_ROFSummaryAccordionContent-' + key}>
                <Text style={styles.accordionText}>
                  {truncateString(i18n.t(key), 40)
                    .replaceAll('$', currencySymbol)
                    .replaceAll('kg', weightUnit)}
                </Text>
                <Text style={styles.accordionText}>
                  {convertInputNumbersToRegionalBasis(
                    value,
                    ROF_DECIMAL_PLACES,
                    true,
                  )}
                </Text>
              </View>
            );
          }
        })}
    </View>
  );
};

export default ROFSummaryAccordionContent;
