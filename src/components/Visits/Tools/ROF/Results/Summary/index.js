// modules
import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

//components
import ROFSummaryAccordion from './ROFSummaryAccordion';
import ROFSummaryAccordionContent from './ROFSummaryAccordionContent';
import ROFReturnOverFeedCostsTable from './ROFReturnOverFeedCostsTable';

// styles
import styles from './styles';

import i18n from '../../../../../../localization/i18n';
import { ROF_FIELDS } from '../../../../../../constants/FormConstants';

const ROFSummaryAccordionContainer = props => {
  const rofToolData = useSelector(state => state.rof?.rofToolData);
  const selectedCategoryTool = useSelector(
    state => state.tool.selectedCategoryTool,
  );
  let summaryData = rofToolData?.[selectedCategoryTool?.toolType]?.summary;

  return (
    <View>
      <View style={styles.formContainer}>
        {summaryData &&
          Object.entries(summaryData)?.map(([sectionKey, fields]) => {
            // Handle return over feed costs sections specially
            if (sectionKey === ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS) {
              const currentData =
                summaryData[ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS] || {};
              const previousData =
                summaryData[ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS] || {};

              return (
                <ROFSummaryAccordion
                  key={'_ROFSummaryAccordion-returnOverFeedCosts'}
                  title={i18n.t('returnOverFeedCosts')}
                  content={
                    <ROFReturnOverFeedCostsTable
                      currentData={currentData}
                      previousData={previousData}
                    />
                  }
                />
              );
            }

            // // Skip the previous return over feed costs section as it's handled above
            // if (sectionKey === ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS) {
            //   return null;
            // }

            return (
              <ROFSummaryAccordion
                key={'_ROFSummaryAccordion-' + sectionKey}
                title={i18n.t(sectionKey)}
                content={<ROFSummaryAccordionContent data={fields} />}
              />
            );
          })}
      </View>
    </View>
  );
};

export default ROFSummaryAccordionContainer;
