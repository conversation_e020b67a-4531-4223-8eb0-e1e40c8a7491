// modules
import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

//components
import ROFSummaryAccordionContent from './ROFSummaryAccordionContent';

// styles
import styles from './styles';

const ROFSummaryAccordionContainer = () => {
  const rofToolData = useSelector(state => state.rof?.rofToolData);
  const selectedCategoryTool = useSelector(
    state => state.tool.selectedCategoryTool,
  );
  let summaryData = rofToolData?.[selectedCategoryTool?.toolType]?.summary;

  return (
    <View>
      <View style={styles.formContainer}>
        {summaryData &&
          Object.entries(summaryData)?.map(([sectionKey, fields]) => {
            return (
              <ROFSummaryAccordionContent
                key={'_ROFSummaryAccordion-' + sectionKey}
                sectionKey={sectionKey}
                data={fields}
                summaryData={summaryData}
              />
            );
          })}
      </View>
    </View>
  );
};

export default ROFSummaryAccordionContainer;
