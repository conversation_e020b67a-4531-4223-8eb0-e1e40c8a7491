// modules
import React from 'react';
import { View } from 'react-native';
import { useSelector } from 'react-redux';

//components
import ROFSummaryAccordion from './ROFSummaryAccordion';
import ROFSummaryAccordionContent from './ROFSummaryAccordionContent';

// styles
import styles from './styles';

import i18n from '../../../../../../localization/i18n';
import { ROF_FIELDS } from '../../../../../../constants/FormConstants';

const ROFSummaryAccordionContainer = () => {
  const rofToolData = useSelector(state => state.rof?.rofToolData);
  const selectedCategoryTool = useSelector(
    state => state.tool.selectedCategoryTool,
  );
  let summaryData = rofToolData?.[selectedCategoryTool?.toolType]?.summary;

  return (
    <View>
      <View style={styles.formContainer}>
        {summaryData &&
          Object.entries(summaryData)?.map(([sectionKey, fields]) => {
            // Handle return over feed costs sections specially
            if (sectionKey === ROF_FIELDS.CURRENT_RETURN_OVER_FEED_COSTS) {
              return (
                <ROFSummaryAccordion
                  key={'_ROFSummaryAccordion-returnOverFeedCosts'}
                  title={i18n.t('returnOverFeedCosts')}
                  content={
                    <ROFSummaryAccordionContent
                      data={fields}
                      isReturnOverFeedCosts={true}
                      summaryData={summaryData}
                    />
                  }
                />
              );
            }

            // Skip the previous return over feed costs section as it's handled above
            if (sectionKey === ROF_FIELDS.PREVIOUS_RETURN_OVER_FEED_COSTS) {
              return null;
            }

            return (
              <ROFSummaryAccordion
                key={'_ROFSummaryAccordion-' + sectionKey}
                title={i18n.t(sectionKey)}
                content={<ROFSummaryAccordionContent data={fields} />}
              />
            );
          })}
      </View>
    </View>
  );
};

export default ROFSummaryAccordionContainer;
